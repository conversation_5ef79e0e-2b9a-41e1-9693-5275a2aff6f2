import { BridgecardService } from '@app/bridgecard';
import { MidenService } from '@app/miden';
import { Injectable } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { CardProvider } from 'src/card/dto/card-provider';
import { CardRepository } from 'src/card/repositories/card.repository';
import { TransactionRepository } from 'src/card/repositories/transaction.repository';
import { UserDataRepository } from 'src/card/repositories/user.repository';
import {
  Events,
  TransactionCategory,
  TransactionStatus,
  TransactionType,
} from 'src/utils/enums';
import { sleep } from 'src/utils/utils';
import { LessThanOrEqual } from 'typeorm';
import { WemaCardService } from '../wema-card/wema-card.service';

@Injectable()
export class CronService {
  constructor(
    private readonly transactionRepository: TransactionRepository,
    private readonly userDataRepository: UserDataRepository,
    private readonly eventEmitter: EventEmitter2,
    private readonly cardRepository: CardRepository,
    private readonly midenService: MidenService,
    private readonly bridgecardService: BridgecardService,
    private readonly wemaCardService: WemaCardService,
  ) {
    console.log('cron intialized');
    // setTimeout(() => {
    // this.handleCron();
    // this.handleCardBalanceUpdateCron();
    // }, 4000);
  }

  @Cron(CronExpression.EVERY_10_MINUTES, {
    name: 'get-miden-token',
    timeZone: 'Africa/Lagos',
  })
  async generateMidenToken() {
    console.log('generateMidenToken cron started');

    await this.midenService.getToken();
  }

  @Cron(CronExpression.EVERY_2_HOURS, {
    name: 'sync-all-card-details-details',
    timeZone: 'Africa/Lagos',
  })
  async fetchMissingCardDetails() {
    console.log('sync-all-card-details cron started');

    await this.userDataRepository.chunk(
      async (data) => {
        if (data?.length > 0) {
          for (const user of data) {
            try {
              console.debug(`card: ${user.id}`);

              this.eventEmitter.emit(Events.SYNC_CARDHOLDER_CARDS, {
                userId: user.id,
              });
            } catch (error) {
              console.log(error);
              console.error(
                `Failed to check status for ${user.id} with error: ${error}`,
              );
            }
          }
        }
      },
      {
        take: 20,
        where: {
          // cardNumber: IsNull(),
        },
        relations: {
          // user: true,
        },
      },
    );
  }

  @Cron(CronExpression.EVERY_2_HOURS, {
    name: 'card-balance-update',
    timeZone: 'Africa/Lagos',
  })
  async handleCardBalanceUpdateCron() {
    console.log('Card Balance Update cron started');

    await this.cardRepository.chunk(
      async (data) => {
        if (data?.length > 0) {
          for (const card of data) {
            try {
              console.debug(`card: ${card.id}`);
              this.eventEmitter.emit(Events.UPDATE_BALANCE, {
                cardId: card.cardId,
                user: card.user,
              });
              await sleep(700);
            } catch (error) {
              console.log(error);
              console.error(
                `Failed to check status for ${card.id} with error: ${error}`,
              );
            }
          }
        }
      },
      {
        where: { provider: CardProvider.MIDEN },
        take: 20,
        // where: {
        //   type: TransactionType.DEBIT,
        //   category: TransactionCategory.WITHDRAW_CARD,
        //   status: TransactionStatus.PENDING,
        // },
        relations: {
          user: true,
        },
      },
    );
  }

  @Cron(CronExpression.EVERY_30_MINUTES, {
    name: 'card-pending-payment-update',
    timeZone: 'Africa/Lagos',
  })
  async handleFundingUpdateCron() {
    console.log('Pending Payment cron started');

    await this.transactionRepository.chunk(
      async (data) => {
        if (data?.length > 0) {
          for (const txn of data) {
            try {
              console.debug(`reference: ${txn.id}`);

              this.eventEmitter.emit(Events.VALIDATE_PENDING_TRANSACTION, {
                id: txn.id,
              });
            } catch (error) {
              console.log(error);
              console.error(
                `Failed to check status for ${txn.id} with error: ${error}`,
              );
            }
          }
        }
      },
      {
        take: 20,
        where: {
          type: TransactionType.CREDIT,
          paymentStatus: TransactionStatus.PENDING,
          provider: CardProvider.MIDEN,
        },
        relations: {
          user: true,
          card: true,
        },
      },
    );
  }

  @Cron(CronExpression.EVERY_5_MINUTES, {
    name: 'card-withdrawal-status',
    timeZone: 'Africa/Lagos',
  })
  async handleWithdrawalCron() {
    console.log('Withdrawal  cron started');
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000); // Current time - 5 minutes

    await this.transactionRepository.chunk(
      async (data) => {
        if (data?.length > 0) {
          for (const txn of data) {
            try {
              console.log(`reference: ${txn.id}`);

              this.eventEmitter.emit(Events.VALIDATE_WITHDRAWAL_TRANSACTION, {
                id: txn.id,
              });
            } catch (error) {
              console.log(error);
              console.error(
                `Failed to check status for ${txn.id} with error: ${error}`,
              );
            }
          }
        }
      },
      {
        take: 20,
        where: {
          createdAt: LessThanOrEqual(fiveMinutesAgo),
          type: TransactionType.DEBIT,
          category: TransactionCategory.WITHDRAW_CARD,
          status: TransactionStatus.PENDING,
          provider: CardProvider.MIDEN,
        },
        relations: {
          user: true,
          card: true,
        },
      },
    );
  }

  @Cron(CronExpression.EVERY_5_MINUTES, {
    name: 'card-funding-update',
    timeZone: 'Africa/Lagos',
  })
  async handleFundingCron() {
    console.log('Funding Update cron started');

    await this.transactionRepository.chunk(
      async (data) => {
        console.log(`data?.length: ${data?.length}`);
        if (data?.length > 0) {
          for (const txn of data) {
            try {
              console.log(`reference: ${txn.id}`);
              this.eventEmitter.emit(Events.VALIDATE_FUNDING_TRANSACTION, {
                id: txn.id,
              });
            } catch (error) {
              console.log(error);
              console.error(
                `Failed to check status for ${txn.id} with error: ${error}`,
              );
            }
          }
        }
      },
      {
        take: 20,
        where: {
          type: TransactionType.CREDIT,
          category: TransactionCategory.FUND_CARD,
          status: TransactionStatus.PENDING,
          provider: CardProvider.MIDEN,
        },
        order: {
          createdAt: 'DESC',
        },
        relations: {
          user: true,
          card: true,
        },
      },
    );
  }

  @Cron(CronExpression.EVERY_DAY_AT_11AM, {
    name: 'fx-rate-update',
    timeZone: 'Africa/Lagos',
  })
  async fetchRate() {
    try {
      // const data = await this.bridgecardService.fxRate();
      const data = await this.midenService.fxRate();

      // if (data?.['NGN-USD']) {
      console.log(data);
      // this.eventEmitter.emit(Events.UPDATE_FX_RATE, {
      //   amount: data?.['NGN-USD'] / 100,
      // });

      this.eventEmitter.emit(Events.UPDATE_FX_RATE, {
        buyRate: data?.buyRate,
        sellRate: data?.sellRate,
      });
    } catch (e) {
      console.log(e);
    }
  }

  @Cron(CronExpression.EVERY_5_MINUTES, {
    name: 'card-topup-sync',
    timeZone: 'Africa/Lagos',
  })
  async handleCron() {
    console.log('Funding Update cron started');

    await this.transactionRepository.chunk(
      async (data) => {
        console.log(`data?.length: ${data?.length}`);
        if (data?.length > 0) {
          for (const txn of data) {
            try {
              console.log(`reference: ${txn.id}`);
              this.eventEmitter.emit(Events.RETRY_CREATION, {
                id: txn.id,
              });
            } catch (error) {
              console.log(error);
              console.error(
                `Failed to check status for ${txn.id} with error: ${error}`,
              );
            }
          }
        }
      },
      {
        take: 20,
        where: {
          type: TransactionType.CREDIT,
          category: TransactionCategory.CREATE_CARD,
          status: TransactionStatus.PENDING,
          paymentStatus: TransactionStatus.SUCCESS,
          provider: CardProvider.MIDEN,
        },
        order: {
          createdAt: 'DESC',
        },
      },
    );
  }

  @Cron(CronExpression.EVERY_10_MINUTES, {
    name: 'card-withdrawal-retry-sync',
    timeZone: 'Africa/Lagos',
  })
  async handleRetryWithdrawalCron() {
    console.log('ard-withdrawal-retry cron started');

    await this.transactionRepository.chunk(
      async (data) => {
        console.log(`data?.length: ${data?.length}`);
        if (data?.length > 0) {
          for (const txn of data) {
            try {
              console.log(`reference: ${txn.id}`);
              this.eventEmitter.emit(Events.RETRY_WITHDRAWAL, {
                reference: txn.reference,
              });
            } catch (error) {
              console.log(error);
              console.error(
                `Failed to check status for ${txn.id} with error: ${error}`,
              );
            }
          }
        }
      },
      {
        take: 20,
        where: {
          type: TransactionType.DEBIT,
          category: TransactionCategory.WITHDRAW_CARD,
          status: TransactionStatus.SUCCESS,
          paymentStatus: TransactionStatus.PENDING,
          provider: CardProvider.MIDEN,
        },
        order: {
          createdAt: 'DESC',
        },
      },
    );
  }

  @Cron(CronExpression.EVERY_MINUTE, {
    name: 'requery-account-generation',
    timeZone: 'Africa/Lagos',
  })
  async requeryAccountGeneration() {
    console.log('requery-account-generation cron started');

    await this.wemaCardService.requeryAccountGeneration(1);

    console.log('requery-account-generation cron completed');
  }

  @Cron(CronExpression.EVERY_10_MINUTES, {
    name: 'requery-funding-processing',
    timeZone: 'Africa/Lagos',
  })
  async requeryFundingProcessing() {
    console.log('requery-funding-processing cron started');

    await this.wemaCardService.requeryProcessing(1);

    console.log('requery-funding-processing cron completed');
  }

  @Cron(CronExpression.EVERY_MINUTE, {
    name: 'requery-pending-processing',
    timeZone: 'Africa/Lagos',
  })
  async requeryPendingProcessing() {
    console.log('requery-pending-processing cron started');

    await this.wemaCardService.reprocessPendingProcessing(1);

    console.log('requery-pending-processing cron completed');
  }

  @Cron(CronExpression.EVERY_10_MINUTES, {
    name: 'requery-pending-payment',
    timeZone: 'Africa/Lagos',
  })
  async requeryPendingPayment() {
    console.log('requery-pending-payment cron started');

    await this.wemaCardService.requeryPendingPayment(1);

    console.log('requery-pending-payment cron completed');
  }

  @Cron(CronExpression.EVERY_10_MINUTES, {
    name: 'requery-pending-refund',
    timeZone: 'Africa/Lagos',
  })
  async requeryPendingRefund() {
    console.log('requery-pending-refund cron started');

    await this.wemaCardService.handleRefund(1);

    console.log('requery-pending-refund cron completed');
  }
}
