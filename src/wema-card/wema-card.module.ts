import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { WemaCardService } from './wema-card.service';
import { WemaAccountController } from './wema-card.controller';
import { WemaCardsController } from './wema-cards.controller';
import { WemaAccountRepository } from './repository/wema.account.repository';
import { AddressRepository } from './repository/address.repository';
import { WemaModule } from '../../libs/wema/src';
import { WemaCardRepository } from './repository/wema-card.repository';
import { WemaFundTransactionRepository } from './repository/wema.fund.transactions';
import { PaymentCacheModule } from '@crednet/utils';
import { Events } from '../utils/enums';
import { WemaExternalTransactionRepository } from './repository/wema.external.transactions';
import { WemaCardVirtualCardCreationWebhookConsumer } from './consumers/wema-card.virtual-card.creation.webhook.consumer';
import { WemaCardTransactionWebhookConsumer } from './consumers/wema-card.transaction.webhook.consumer';
import { WemaFundTransactionWebhookConsumer } from './consumers/wema.fund.transaction.webhook.consumer';
import { WemaCardAccountGenerationWebhookConsumer } from './consumers/wema-card.account.generation.webhook.consumer';
import { WemaCardAddressValidationWebhookConsumer } from './consumers/wema-card.address.validation.webhook.consumer';
import { WemaCardPaymentConsumer } from './consumers/wema-card.payment.consumer';

@Module({
  imports: [
    WemaModule,
    PaymentCacheModule,
    BullModule.registerQueue({
      name: Events.REQUERY_FUND_WEMA_CARD,
    }),
  ],
  controllers: [WemaAccountController, WemaCardsController],
  providers: [
    WemaCardService,
    WemaAccountRepository,
    AddressRepository,
    WemaCardRepository,
    WemaFundTransactionRepository,
    WemaCardVirtualCardCreationWebhookConsumer,
    WemaExternalTransactionRepository,
    WemaCardTransactionWebhookConsumer,
    WemaFundTransactionWebhookConsumer,
    WemaCardAccountGenerationWebhookConsumer,
    WemaCardAddressValidationWebhookConsumer,
    WemaCardPaymentConsumer,
  ],
  exports: [WemaCardService],
})
export class WemaCardModule {}
